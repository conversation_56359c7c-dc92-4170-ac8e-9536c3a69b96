import React, { memo, useEffect } from 'react';

import { useFormContext, useWatch } from 'react-hook-form';

import { states } from '@/utils/constants/master';
import {
  formatToAlphaNumeric,
  formatToAlphaNumericToUpperCase,
  formatToNumber,
  formatToNumberWithDot,
} from '@/utils/format-value';

import { countryOptions } from '@/constants/mrd/manage-patient/country';
import {
  genderOptions,
  maritalStatusOptions,
} from '@/constants/mrd/manage-patient/select-options';

import ControlledDatePicker from '@/components/controlled-inputs/ControlledDatePicker';
import ControlledSelectField from '@/components/controlled-inputs/ControlledSelectField';
import ControlledTextField from '@/components/controlled-inputs/ControlledTextField';

import AppTitle from '@/core/components/app-title';
import { PatientDetails } from '@/types/mrd/manage-patient/patient-details';

const GeneralDetails = () => {
  const {
    control,
    setValue,
    clearErrors,
    formState: { errors },
  } = useFormContext<PatientDetails>();

  // Watch required fields
  const name = useWatch({ control, name: 'name' });
  const dob = useWatch({ control, name: 'dob' });
  const sex = useWatch({ control, name: 'sex' });

  useEffect(() => {
    if (name && errors.name) {
      clearErrors('name');
    }
  }, [name, errors.name, clearErrors]);

  useEffect(() => {
    if (dob && errors.dob) {
      clearErrors('dob');
    }
  }, [dob, errors.dob, clearErrors]);

  useEffect(() => {
    if (sex && errors.sex) {
      clearErrors('sex');
    }
  }, [sex, errors.sex, clearErrors]);

  // Watch the dob field
  useEffect(() => {
    if (dob) {
      const calculateAge = (dob: string) => {
        const birthDate = new Date(dob);
        const today = new Date();
        let age = today.getFullYear() - birthDate.getFullYear();
        const monthDiff = today.getMonth() - birthDate.getMonth();
        if (
          monthDiff < 0 ||
          (monthDiff === 0 && today.getDate() < birthDate.getDate())
        ) {
          age--;
        }
        return age;
      };

      const age = calculateAge(dob);
      setValue('age', age.toString()); // Prefill the age field
    }
  }, [dob, setValue]);

  return (
    <div className="flex flex-col gap-base w-full max-h-full h-full overflow-y-auto">
      <div className="w-full flex flex-col gap-base border-b-2 py-base">
        <div className="flex gap-base w-[60%]">
          <div className="w-1/2">
            <ControlledTextField
              name="name"
              control={control}
              label="Patient Name"
              placeholder="Enter Patient Name"
              fullWidth
              required
              initiallyReadonly
              formatValue={formatToAlphaNumericToUpperCase}
            />
          </div>
          <div className="w-1/4">
            <ControlledDatePicker
              name="dob"
              control={control}
              label="Date of Birth"
              initiallyReadonly
              disableFuture={true}
            />
          </div>
          <div className="w-1/4">
            <ControlledSelectField
              name="sex"
              control={control}
              label="Gender"
              options={genderOptions}
              placeholder="Select"
              required
              initiallyReadonly
            />
          </div>
        </div>
        <div className="flex gap-base ">
          <div className="flex-1 min-w-[120px]">
            <ControlledTextField
              name="age"
              control={control}
              label="Age"
              required
              placeholder="Enter"
              formatValue={formatToNumber}
            />
          </div>
          <div className="flex-1  min-w-[130px]">
            <ControlledTextField
              name="height"
              control={control}
              label="Height"
              required
              placeholder="Height in cms"
              formatValue={formatToNumber}
            />
          </div>
          <div className="flex-1  min-w-[120px]">
            <ControlledTextField
              name="weight"
              control={control}
              required
              label="Weight"
              placeholder="Weight in kgs"
              formatValue={formatToNumberWithDot(1)}
            />
          </div>
          <div className="flex-[1.3]  min-w-[130px]">
            <ControlledSelectField
              name="maritalStatus"
              control={control}
              label="Marital Status"
              options={maritalStatusOptions}
              placeholder="Select"
            />
          </div>
          <ControlledTextField
            name="cmcId"
            control={control}
            label="CMC ID"
            placeholder="Enter CMC ID"
            initiallyReadonly
            formatValue={formatToAlphaNumericToUpperCase}
            slotProps={{
              input: {
                inputProps: { maxLength: 9 },
              },
            }}
          />
        </div>
      </div>
      <div className="w-[60%] flex flex-col gap-base py-base">
        <AppTitle variant="subtitle1">Patient Address</AppTitle>
        <div className="flex gap-base w-full">
          <ControlledTextField
            name="address.houseName"
            control={control}
            label="House Name"
            placeholder="House No/Name"
            fullWidth
            initiallyReadonly
            formatValue={formatToAlphaNumeric}
          />
          <ControlledTextField
            name="address.pin"
            control={control}
            label="Pin"
            placeholder="682030"
            fullWidth
            initiallyReadonly
            formatValue={formatToNumber}
            slotProps={{
              input: {
                inputProps: { maxLength: 6 },
              },
            }}
          />
        </div>
        <div className="flex gap-base w-full">
          <ControlledTextField
            name="address.street"
            control={control}
            label="Street"
            placeholder="Street"
            fullWidth
            initiallyReadonly
            formatValue={formatToAlphaNumeric}
          />
          <ControlledTextField
            name="address.city"
            control={control}
            label="City"
            placeholder="City"
            required
            fullWidth
            initiallyReadonly
            formatValue={formatToAlphaNumeric}
          />
        </div>
        <div className="flex gap-base w-full">
          <ControlledTextField
            name="address.district"
            control={control}
            label="District"
            placeholder="District"
            initiallyReadonly
          />
          <ControlledSelectField
            name="address.state"
            control={control}
            label="State"
            options={states}
            placeholder="State"
            initiallyReadonly
          />
        </div>
        <div className="flex gap-base w-1/2">
          <ControlledSelectField
            name="address.country"
            control={control}
            label="Country"
            options={countryOptions}
            placeholder="Country"
            initiallyReadonly
          />
        </div>
        <div className="flex gap-base w-full">
          <ControlledTextField
            name="contact.phone"
            control={control}
            label="Mobile (Personal)"
            placeholder="Phone"
            fullWidth
            initiallyReadonly
            formatValue={formatToNumber}
            required
            slotProps={{
              input: {
                inputProps: { maxLength: 10 },
              },
            }}
            rules={{
              validate: (value: any) => {
                if (value?.includes('*')) return true;

                if (!value) return 'Mobile number is required';
                if (value.length !== 10)
                  return 'Enter a valid 10 digit mobile number';
                return true;
              },
            }}
          />

          <ControlledTextField
            name="contact.email"
            control={control}
            label="Email"
            placeholder="Email"
            fullWidth
            initiallyReadonly
          />
        </div>
      </div>
    </div>
  );
};

export default memo(GeneralDetails);

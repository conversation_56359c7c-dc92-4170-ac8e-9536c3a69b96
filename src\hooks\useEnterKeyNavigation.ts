import { useCallback } from 'react';

export interface FieldNavigationConfig {
  fieldOrder: string[];
  isFieldDisabled?: (fieldName: string, index?: number) => boolean;
  shouldTriggerSave?: (fieldName: string) => boolean;
  onSave?: () => void;
}

/**
 * Custom hook for handling Enter key navigation between form fields
 * Based on the implementation from GeneralDetails.tsx
 */
export const useEnterKeyNavigation = (config: FieldNavigationConfig) => {
  const { fieldOrder, isFieldDisabled, shouldTriggerSave, onSave } = config;

  const getNextEditableField = useCallback(
    (currentField: string): string | null => {
      const currentIndex = fieldOrder.indexOf(currentField);

      for (let i = currentIndex + 1; i < fieldOrder.length; i++) {
        const fieldName = fieldOrder[i];
        // Check if field is editable (not disabled)
        if (!isFieldDisabled || !isFieldDisabled(fieldName)) {
          return fieldName;
        }
      }
      return null;
    },
    [fieldOrder, isFieldDisabled]
  );

  const handleKeyDown = useCallback(
    (
      e: React.KeyboardEvent,
      currentFieldName: string,
      options?: {
        shouldTriggerSave?: boolean;
        index?: number;
        customFieldName?: string;
      }
    ) => {
      if (e.key === 'Enter') {
        e.preventDefault();

        const fieldNameToUse = options?.customFieldName || currentFieldName;
        const shouldSave =
          options?.shouldTriggerSave ||
          (shouldTriggerSave && shouldTriggerSave(fieldNameToUse));

        if (shouldSave) {
          // Trigger save for the last field or specific fields
          if (onSave) {
            onSave();
          } else {
            // Fallback to finding and clicking submit button
            const form = e.currentTarget.closest('form');
            if (form) {
              const submitButton = form.querySelector(
                'button[type="submit"]'
              ) as HTMLButtonElement;
              if (submitButton) {
                submitButton.click();
              }
            }
          }
          return;
        }

        const nextField = getNextEditableField(fieldNameToUse);
        if (nextField) {
          // Try different selectors to find the next field
          const selectors = [
            `[name="${nextField}"]`,
            `[id="${nextField}"]`,
            `[name*="${nextField}"]`,
            `input[name="${nextField}"]`,
            `textarea[name="${nextField}"]`,
            `select[name="${nextField}"]`,
            `input[id="${nextField}"]`,
            `textarea[id="${nextField}"]`,
            `select[id="${nextField}"]`,
            `[data-field="${nextField}"]`,
          ];

          let nextFieldElement: HTMLElement | null = null;

          for (const selector of selectors) {
            nextFieldElement = document.querySelector(selector) as HTMLElement;
            if (
              nextFieldElement &&
              !nextFieldElement.hasAttribute('disabled')
            ) {
              break;
            }
          }

          if (nextFieldElement) {
            // Handle different types of form elements
            if (
              nextFieldElement.tagName === 'INPUT' ||
              nextFieldElement.tagName === 'TEXTAREA'
            ) {
              (
                nextFieldElement as HTMLInputElement | HTMLTextAreaElement
              ).focus();
            } else if (nextFieldElement.tagName === 'SELECT') {
              (nextFieldElement as HTMLSelectElement).focus();
            } else {
              // For custom components, try to find focusable elements within
              const focusableElement = nextFieldElement.querySelector(
                'input, textarea, select, button, [tabindex]:not([tabindex="-1"])'
              ) as HTMLElement;
              if (focusableElement) {
                focusableElement.focus();
              } else {
                nextFieldElement.focus();
              }
            }
          }
        }
      }
    },
    [getNextEditableField, shouldTriggerSave, onSave]
  );

  return {
    handleKeyDown,
    getNextEditableField,
  };
};

/**
 * Utility function to create field navigation for array-based forms (like emergency contacts, qualifications, etc.)
 */
export const createArrayFieldNavigation = (
  baseFields: string[],
  index: number,
  prefix: string
): string[] => {
  return baseFields.map((field) => `${prefix}.${index}.${field}`);
};

/**
 * Utility function to extract base field name from array field name
 * e.g., "emergencyContacts.0.name" -> "name"
 */
export const getBaseFieldName = (fieldName: string): string => {
  const parts = fieldName.split('.');
  return parts[parts.length - 1];
};

'use client';

import React, { memo, useState, useEffect } from 'react';

// Components
import {
  getTimeBasedGreeting,
  getLastUpdatedText,
} from '@/utils/mrd/dashboard/greeting';

import { PERMISSION_KEYS } from '@/constants/permission-keys';
import { routes } from '@/constants/routes';

import ChartPlaceholder from '@/views/shared/dashboard/ChartPlaceholder';
import DashboardCalendar from '@/views/shared/dashboard/DashboardCalendar';
import DashboardHeader from '@/views/shared/dashboard/DashboardHeader';

import { ProtectedRoute } from '@/components/permission/protected-route';

import { REDASH_MRD_URL } from '@/core/configs/dashboard/dashboard-redash';

// Reusable skeleton from EMR lifestyle

const Dashboard = () => {
  const [greeting, setGreeting] = useState<string>('');
  const [lastUpdated, setLastUpdated] = useState<string>('');
  const [lastRefreshTime, setLastRefreshTime] = useState<Date | null>(null);
  const [reloadKey, setReloadKey] = useState(0);

  // Update greeting and last updated text every minute
  useEffect(() => {
    const updateGreeting = () => {
      setGreeting(getTimeBasedGreeting());
      setLastUpdated(getLastUpdatedText(lastRefreshTime || undefined));
    };

    updateGreeting();
    const interval = setInterval(updateGreeting, 60000); // Update every minute

    return () => clearInterval(interval);
  }, [lastRefreshTime]);

  // Set initial refresh time - No local data loading needed
  useEffect(() => {
    const now = new Date();
    setLastRefreshTime(now);
    setLastUpdated(getLastUpdatedText(now));
    setReloadKey(0);
  }, []);

  const handleRefresh = async () => {
    const now = new Date();
    setLastRefreshTime(now);
    setLastUpdated(getLastUpdatedText(now));
    setReloadKey((prev) => prev + 1);
  };

  return (
    <div className="h-full w-full flex flex-col p-6 bg-gray-50 overflow-y-auto">
      <DashboardHeader
        greeting={greeting}
        lastUpdated={lastUpdated}
        onRefresh={handleRefresh}
        isLoading={false}
        lastUpdatedMargin="mb-6"
      />

      <div className="w-full mb-4">
        <ChartPlaceholder
          title="MRD Dashboard Analytics"
          colSpan="col-span-4"
          minHeight="170vh"
          embedUrl={`${REDASH_MRD_URL}&reload=${reloadKey}`}
          hideRedashFooter={true}
        />
      </div>

      {/* Calendar - Full width below Redash */}
      <div className="w-1/4 mb-4">
        <DashboardCalendar />
      </div>
    </div>
  );
};

const ProtectedDashboard = () => {
  return (
    <ProtectedRoute
      requiredPermissions={[PERMISSION_KEYS.MRD_DASHBOARD_VIEW]}
      redirectPath={routes.MRD_MANAGE_PATIENTS}
    >
      <Dashboard />
    </ProtectedRoute>
  );
};

export default memo(ProtectedDashboard);

'use client';

import React, { memo, useCallback, useEffect } from 'react';

import { DatePicker } from '@/lib/common/date_picker';
import EMRQueueCard from '@/lib/QueueCard';

import { fetchAppointments, useDoctorStore } from '@/store/mrd/queue/doctor';
import { useUserStore } from '@/store/userStore';

import colors from '@/utils/colors';

import { PERMISSION_KEYS } from '@/constants/permission-keys';

import ConsultationSelect from '@/views/mrd/queue/consultation-select';
import QueueList from '@/views/mrd/queue/queue-list';

import AppTitle from '@/core/components/app-title';

const MrdPatientQueue = () => {
  const {
    consultingQueueItem: consultingAppointment,
    date,
    setDate,
    selectedDoctor,
    appointmentsByDoctor,
    loadSelectedDoctorAppointments,
  } = useDoctorStore();

  const { permissions = [] } = useUserStore();
  const hasQueueManagePermission = permissions.includes(
    PERMISSION_KEYS.MRD_PATIENT_QUEUE_MANAGE
  );

  useEffect(() => {
    const unsubscribe = useUserStore.subscribe(
      (state) => state.currentAppointment,
      (currentAppointment) => {
        if (selectedDoctor) {
          if (currentAppointment) {
            useDoctorStore.getState().setConsultingQueueItem(
              {
                ...currentAppointment,
                time: currentAppointment.time,
                queueId: currentAppointment.queueId,
                queuePosition: currentAppointment.queuePosition,
                department: currentAppointment.department,
                patient: currentAppointment.patient,
                patientStatus: currentAppointment.patientStatus,
                status: currentAppointment.status,
              },
              selectedDoctor.id
            );
          } else {
            useDoctorStore
              .getState()
              .setConsultingQueueItem(null, selectedDoctor.id);
          }
          fetchAppointments(selectedDoctor.id);
        }
      }
    );

    const handleStorageChange = (e: StorageEvent) => {
      if (e.key === 'emrAction' && e.newValue && selectedDoctor) {
        const actionData = JSON.parse(e.newValue);

        if (actionData.doctorId === selectedDoctor.id) {
          if (
            actionData.action === 'cancel' ||
            actionData.action === 'markConsulted'
          ) {
            useDoctorStore
              .getState()
              .setConsultingQueueItem(null, selectedDoctor.id);
            fetchAppointments(selectedDoctor.id);
          }
        }
      }
    };

    window.addEventListener('storage', handleStorageChange);

    return () => {
      unsubscribe();
      window.removeEventListener('storage', handleStorageChange);
    };
  }, [selectedDoctor]);

  const handleDoctorChange = useCallback(async () => {
    if (selectedDoctor) {
      fetchAppointments(selectedDoctor.id);
    }
  }, [selectedDoctor]);

  useEffect(() => {
    loadSelectedDoctorAppointments();
  }, [appointmentsByDoctor, loadSelectedDoctorAppointments]);

  useEffect(() => {
    if (selectedDoctor) {
      fetchAppointments(selectedDoctor.id);
    }
  }, [selectedDoctor, date]);

  return (
    <div className="w-[30%] h-full rounded-base shadow-base">
      <div
        className={`rounded-base border border-[${colors.common.ashGray}] bg-white flex flex-col box-border h-full`}
      >
        <div className="flex items-center justify-between p-base">
          <AppTitle>Consultation List</AppTitle>

          {hasQueueManagePermission && (
            <div className="flex gap-base">
              <ConsultationSelect />
              <DatePicker
                value={date}
                onChange={(newDate) => {
                  setDate(newDate);
                }}
              />
            </div>
          )}
        </div>
        <div
          className={`flex flex-col rounded-lg p-base bg-[#FCFCFC] h-full overflow-auto`}
        >
          {hasQueueManagePermission ? (
            <>
              <div className="flex items-center justify-between gap-8">
                <div className="font-medium text-base text-[#637D92] -tracking-[2.2%]">
                  Current Patient
                </div>
                <div className="flex items-center gap-base"></div>
              </div>
              <div className="py-base">
                {consultingAppointment ? (
                  <EMRQueueCard
                    time={consultingAppointment.time}
                    patientId={consultingAppointment.patient.id}
                    patient={consultingAppointment.patient}
                    queueId={consultingAppointment.queueId}
                    queuePosition={consultingAppointment.queuePosition}
                    patientStatus={consultingAppointment.patientStatus}
                    status={consultingAppointment.status}
                  />
                ) : (
                  <div className="h-8 border border-[#323F4940] rounded bg-[#DAE1E7]"></div>
                )}
              </div>

              <h2
                className={`
                  font-medium text-base text-[#637D92] mb-4
                `}
              >
                Patient Queue
              </h2>
              <div className="flex-grow h-full">
                <QueueList />
              </div>
            </>
          ) : (
            <div className="h-full flex flex-col items-center justify-center text-center p-6">
              <div className="p-4 rounded-lg w-full">
                <h3 className="text-lg font-medium -800">Access Denied</h3>
                <p className="text-sm mt-1">
                  You don&apos;t have permission to view or manage the
                  consultation list and patient queue.
                </p>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default memo(MrdPatientQueue);

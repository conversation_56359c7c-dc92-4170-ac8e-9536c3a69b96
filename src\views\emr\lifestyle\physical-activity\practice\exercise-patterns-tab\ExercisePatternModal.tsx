import React, { memo, useCallback, useEffect, useMemo, useState } from 'react';

import { FormProvider, useForm } from 'react-hook-form';

import { toast } from 'sonner';

import { useCurrentPatientStore } from '@/store/currentPatientStore';
import { useDoctorStore } from '@/store/emr/doctor-profile/personal-info';
import { lifestyleStore } from '@/store/emr/lifestyle/lifestyle-store';
import { exercisePatternStore } from '@/store/emr/lifestyle/physical-activity/practice/exercise-pattern-store';

import {
  LifestyleMode,
  LifestyleRecordStatus,
} from '@/constants/emr/lifestyle';

import LifestyleModalWrapper from '@/views/emr/lifestyle/shared/LifestyleModalWrapper';

import { QuestionnaireResponse } from '@/types/emr/lifestyle/questionnaire';

import ExercisePatternForm from './ExercisePatternForm';

const hasFormData = (data: QuestionnaireResponse): boolean => {
  if (!data?.questions) return false;

  return data.questions.some((question) =>
    question.fields?.some((field) => {
      if (field.type === 'table' && field.value) {
        return (
          Array.isArray(field.value) &&
          field.value.length > 0 &&
          field.value.some((row) =>
            Object.values(row || {}).some(
              (value) => value !== null && value !== undefined && value !== ''
            )
          )
        );
      }

      return (
        field.value !== null && field.value !== undefined && field.value !== ''
      );
    })
  );
};

const getValidationMessage = (data: QuestionnaireResponse): string => {
  if (!data?.questions) return 'Please enter at least one record to save';

  const tableFields = data.questions.flatMap(
    (question) =>
      question.fields?.filter((field) => field.type === 'table') || []
  );

  if (tableFields.length === 0)
    return 'Please enter at least one record to save';

  const hasRows = tableFields.some(
    (field) => Array.isArray(field.value) && field.value.length > 0
  );

  if (!hasRows) {
    return 'Please enter at least one record to save';
  }

  return 'Please enter at least one record to save';
};

// Function to check if there are any completely empty rows
const hasEmptyRows = (data: QuestionnaireResponse): boolean => {
  if (!data?.questions) return false;

  return data.questions.some((question) =>
    question.fields?.some((field) => {
      if (field.type === 'table' && Array.isArray(field.value)) {
        return field.value.some((row) => {
          if (!row) return true; // null or undefined row is considered empty

          // Check if all values in the row are empty
          const values = Object.values(row);
          return (
            values.length > 0 &&
            values.every((value) => {
              if (Array.isArray(value)) {
                return value.length === 0;
              }
              return value === null || value === undefined || value === '';
            })
          );
        });
      }
      return false;
    })
  );
};

import type { MutableRefObject } from 'react';
const ExercisePatternModal: React.FC<{
  patientData?: QuestionnaireResponse | null;
  mode?: LifestyleMode;
  onAfterSubmit?: () => void;
  hideSaveButton?: boolean;
  onSaveRef?: MutableRefObject<(() => void) | null>;
  initialValues?: any;
}> = ({
  patientData,
  mode = LifestyleMode.CREATE,
  onAfterSubmit,
  hideSaveButton = false,
  onSaveRef,
  initialValues,
}) => {
  const {
    getLifestyleQuestions,
    questions,
    questionLoading,
    updating,
    createLifestyleData,
    updateLifestyleData,
    patientData: storePatientData,
  } = exercisePatternStore();
  const { setModalOpen } = lifestyleStore();
  const profile = useDoctorStore((state) => state.doctorProfile);
  const { patient } = useCurrentPatientStore();

  const [currentMode, setCurrentMode] = useState(mode);
  const [saveAttempted, setSaveAttempted] = useState(false);

  const methods = useForm<QuestionnaireResponse>({
    defaultValues: patientData ?? initialValues ?? questions,
    mode: 'onChange',
  });

  const { watch, getValues } = methods;

  const watchedData = watch();
  const isFormValid = useMemo(() => {
    if (currentMode === LifestyleMode.VIEW) {
      return true;
    }

    return hasFormData(watchedData);
  }, [watchedData, currentMode]);

  const formFields = useMemo(() => {
    if (!questions?.questions?.length) return [];

    return questions.questions;
  }, [questions]);

  const onSubmit = useCallback(
    async (data: QuestionnaireResponse) => {
      setSaveAttempted(true);

      // Check for empty rows first
      if (
        (currentMode === LifestyleMode.CREATE ||
          currentMode === LifestyleMode.EDIT) &&
        hasEmptyRows(data)
      ) {
        toast.error("Empty row can't be saved", {
          style: {
            background: '#fef2f2',
            border: '1px solid #fecaca',
            color: '#dc2626',
            padding: '12px 16px',
            borderRadius: '6px',
          },
          closeButton: true,
          duration: 4000,
        });
        return;
      }

      if (
        (currentMode === LifestyleMode.CREATE ||
          currentMode === LifestyleMode.EDIT) &&
        !hasFormData(data)
      ) {
        const validationMessage = getValidationMessage(data);
        toast.error(validationMessage, {
          style: {
            background: '#fef2f2',
            border: '1px solid #fecaca',
            color: '#dc2626',
            padding: '12px 40px 12px 16px',
            borderRadius: '6px',
            position: 'relative',
          },
          closeButton: true,
          duration: 5000,
          className: 'custom-error-toast',
        });
        return;
      }

      try {
        const cleanedData = {
          ...data,
          questions: data.questions?.map((question) => ({
            ...question,
            fields: question.fields?.map((field) => {
              if (field.type === 'table' && Array.isArray(field.value)) {
                const filteredRows = field.value.filter((row) => {
                  if (!row) return false;

                  return Object.values(row).some((value) => {
                    if (Array.isArray(value)) {
                      return value.length > 0;
                    }
                    return (
                      value !== null && value !== undefined && value !== ''
                    );
                  });
                });
                return {
                  ...field,
                  value: filteredRows,
                };
              }
              return field;
            }),
          })),
        };

        const hasValidData = cleanedData.questions?.some((question) =>
          question.fields?.some((field) => {
            if (field.type === 'table' && Array.isArray(field.value)) {
              return field.value.length > 0;
            }
            return (
              field.value !== null &&
              field.value !== undefined &&
              field.value !== ''
            );
          })
        );

        if (!hasValidData) {
          toast.error('Please enter at least one complete row before saving', {
            style: {
              background: '#fef2f2',
              border: '1px solid #fecaca',
              color: '#dc2626',
              padding: '12px 16px',
              borderRadius: '6px',
            },
            duration: 4000,
          });
          return;
        }

        if (cleanedData?.id) {
          const updateData = {
            ...cleanedData,
            doctor: {
              id: profile?.id ?? '',
              name: profile?.general?.fullName,
              designation: profile?.general?.designation,
            },
          };
          await updateLifestyleData(updateData);
        } else {
          const createData = {
            ...cleanedData,
            doctor: {
              id: profile?.id ?? '',
              name: profile?.general?.fullName,
              designation: profile?.general?.designation,
            },
            // Include ambient listening data if available from initialValues
            ...(initialValues?.conversation && {
              conversation: initialValues.conversation,
            }),
            ...(initialValues?.recordingDuration && {
              recordingDuration: initialValues.recordingDuration,
            }),
          };
          await createLifestyleData(createData);
        }
        setModalOpen(false);
        onAfterSubmit?.();
      } catch (error) {
        console.error('Error submitting exercise pattern:', error);
      }
    },
    [
      setModalOpen,
      updateLifestyleData,
      profile?.general?.fullName,
      profile?.general?.designation,
      profile?.id,
      createLifestyleData,
      onAfterSubmit,
      currentMode,
      initialValues, // Add initialValues to dependencies
    ]
  );

  const handleSaveClick = useCallback(() => {
    const currentData = getValues();
    onSubmit(currentData);
  }, [getValues, onSubmit]);

  // Set up the save ref when the component mounts/updates
  useEffect(() => {
    if (onSaveRef) {
      onSaveRef.current = handleSaveClick;
    }
    return () => {
      if (onSaveRef) {
        onSaveRef.current = null;
      }
    };
  }, [onSaveRef, handleSaveClick]);

  useEffect(() => {
    getLifestyleQuestions();
  }, [getLifestyleQuestions]);

  useEffect(() => {
    if (patientData) {
      methods.reset(patientData);
    } else if (questions) {
      methods.reset(questions);
    }
  }, [patientData, questions, methods]);

  return (
    <FormProvider {...methods}>
      <LifestyleModalWrapper
        loading={questionLoading}
        onSubmit={handleSaveClick}
        updating={updating}
        mode={currentMode}
        onEdit={() => setCurrentMode(LifestyleMode.EDIT)}
        finalized={patientData?.status === LifestyleRecordStatus.FINALIZED}
        hideSaveButton={hideSaveButton}
        data={patientData || undefined}
        doctorName={profile?.general?.fullName}
        patientName={patient?.name}
      >
        <ExercisePatternForm
          formFields={formFields}
          readonly={currentMode === LifestyleMode.VIEW}
          showHeading={true}
          mode={currentMode}
          patientData={storePatientData}
          variant="modal"
        />
      </LifestyleModalWrapper>
    </FormProvider>
  );
};

export default memo(ExercisePatternModal);

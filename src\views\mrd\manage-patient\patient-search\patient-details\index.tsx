import React, { memo, useEffect, useMemo } from 'react';

import { capitalize } from '@mui/material';

import { useMrdPatientSearch } from '@/store/mrd/manage-patient/patient-search';

import { getOrDefault } from '@/utils/common';
import { DateFormats } from '@/utils/dateUtils/dateFormats';
import { formatDate } from '@/utils/dateUtils/dayUtils';
import { formatAddress } from '@/utils/mrd/manage-patient/format-address';

import KeyValuePair, {
  KeyValuePairProps,
} from '@/views/mrd/manage-patient/shared/KeyValuePair';

import PatientVitals from './PatientVitals';
import UpcomingAppointments from './upcoming-appointments';

const PatientDetails = () => {
  const { patient, refreshPatient } = useMrdPatientSearch();

  const patientDetails = useMemo<KeyValuePairProps[]>(() => {
    const details = [
      { label: 'Name', value: getOrDefault(patient?.name) },
      { label: 'Patient ID', value: getOrDefault(patient?.id) },
    ];

    if ((patient as any)?.cmcId) {
      details.push({
        label: 'CMC ID',
        value: getOrDefault((patient as any).cmcId),
      });
    }

    return [
      ...details,
      { label: 'Age', value: getOrDefault(patient?.age) },
      {
        label: 'Gender',
        value: patient?.sex ? capitalize(String(patient.sex)) : '--',
      },
      {
        label: 'Marital Status',
        value: getOrDefault(patient?.maritalStatus, '-'),
      },
      {
        label: 'Last Visited',
        value: getOrDefault(
          formatDate(
            patient?.last_consultation_date,
            DateFormats.DATE_DD_MM_YYYY_SLASH
          ),
          'N/A'
        ),
      },
    ];
  }, [patient]);

  const patientAddress = useMemo<KeyValuePairProps[]>(() => {
    return [
      {
        label: 'Phone Number',
        value: getOrDefault(patient?.contact?.phone, '-'),
      },
      {
        label: 'Address',
        value: formatAddress(patient?.address, { fallback: '-' }),
      },
    ];
  }, [patient]);

  useEffect(() => {
    refreshPatient();
  }, [refreshPatient]);

  return (
    <div className="flex flex-col p-1 h-full">
      <div className="flex gap-base border-b py-base">
        {patientDetails.map((pair) => (
          <KeyValuePair
            key={pair.label}
            label={pair.label}
            value={pair.value}
          />
        ))}
      </div>
      <div className="border-b py-base w-full">
        <div className="w-1/2 gap-base flex">
          {patientAddress.map((pair) => (
            <KeyValuePair
              key={pair.label}
              label={pair.label}
              value={pair.value}
            />
          ))}
        </div>
      </div>
      <UpcomingAppointments id={getOrDefault(patient?.id)} />
      {/* TODO: Uncomment when reports are implemented */}
      {/* <ReportButtons /> */}
      <PatientVitals />
    </div>
  );
};

export default memo(PatientDetails);

'use client';

import { FC, useCallback, useEffect, useRef, useState } from 'react';

import * as speechsdk from 'microsoft-cognitiveservices-speech-sdk';
import { BiX } from 'react-icons/bi';
import { TbPlayerPause, TbPlayerPlay } from 'react-icons/tb';

import { getDoctorProfile } from '@/query/emr/doctor-profile/personal-info';

import { throttledUpdateLastActivity } from '@/utils/session';

import SineWaves from '@/helpers/sine-waves/sine-waves';

import {
  recordingStates,
  RecordingState,
  waves,
  languageOptions,
  CurrentModal,
  currentModal,
} from '@/constants/ambient-listening';

import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';

import PrimaryButton from '@/core/components/primary-button';

interface RecordConsultationProps {
  engine: speechsdk.ConversationTranscriber | null;
  onRecordEnd: (transcript: string, duration: number) => void;
  handleChooseLanguage: (newLanguage: string) => void;
  selectedLanguage: string;
  currentMode: CurrentModal;
  setCurrentMode: (mode: CurrentModal) => void;
  onCancel?: () => void;
  isLanguageSelection?: boolean;
  onEngineReconnect?: () => Promise<void>;
}

const { IDLE, PAUSED, RECORDING } = recordingStates;
const { LANGUAGE_SELECTION, RECORD_CONSULTATION } = currentModal;

const PERIODIC_ACTION_INTERVAL = 5 * 60 * 1000;
const RECONNECTION_INTERVAL = 5 * 60 * 1000; // Reconnect every 15 minutes to prevent token expiration

const RecordConsultation: FC<RecordConsultationProps> = ({
  engine,
  onRecordEnd,
  handleChooseLanguage,
  selectedLanguage,
  setCurrentMode,
  currentMode,
  onCancel,
  isLanguageSelection,
  onEngineReconnect,
}) => {
  const intervalRef = useRef<NodeJS.Timeout | null>(null);
  const reconnectionIntervalRef = useRef<NodeJS.Timeout | null>(null);

  const [recordingState, setRecordingState] = useState<RecordingState>(IDLE);
  const [wavesConfig, setWavesConfig] = useState({
    speed: 0,
    wavesWidth: '0',
  });

  //speech states
  const [currentOffset, setCurrentOffset] = useState(0);
  const [isListening, setIsListening] = useState(false);
  const [isSpeaking, setIsSpeaking] = useState(false);
  const [isReconnecting, setIsReconnecting] = useState(false);

  // Track total recording duration across pause/resume cycles
  const [totalRecordingTime, setTotalRecordingTime] = useState(0);
  const [lastResumeTime, setLastResumeTime] = useState<number | null>(null);
  const durationIntervalRef = useRef<NodeJS.Timeout | null>(null);

  const [transcriptHistory, setTranscriptHistory] = useState(['']);
  const [transcript, setTranscript] = useState('');
  const [tEvent, setTvent] = useState(null);

  const transcriptEl = useRef(null);
  const silenceTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  const wavesEl = useRef(null);

  const handlePeriodicAction = useCallback(() => {
    getDoctorProfile();
  }, []);

  const handleTranscribing = useCallback((s: any, e: any) => {
    throttledUpdateLastActivity();
    if (e && e.privResult && e.privResult.privJson) {
      const event = JSON.parse(e.privResult.privJson);
      setTvent(() => event);

      // Detect speech - set speaking to true and clear silence timeout
      setIsSpeaking(true);
      if (silenceTimeoutRef.current) {
        clearTimeout(silenceTimeoutRef.current);
      }

      // Set timeout to detect silence (2 seconds of no speech)
      silenceTimeoutRef.current = setTimeout(() => {
        setIsSpeaking(false);
      }, 600);

      // Auto-scroll to bottom when new transcript comes in
      if (transcriptEl.current) {
        setTimeout(() => {
          (transcriptEl.current as HTMLDivElement).scrollTop = (
            transcriptEl.current as HTMLDivElement
          ).scrollHeight;
        }, 0);
      }
    }
  }, []);

  const handleCancelRecord = () => {
    if (engine && isListening) {
      engine.stopTranscribingAsync();
    }
    setRecordingState(IDLE);
    setIsListening(false);
    setIsSpeaking(false);
    setTotalRecordingTime(0);
    setLastResumeTime(null);
    setTranscript('');
    setTranscriptHistory(['']);

    if (intervalRef.current) {
      clearInterval(intervalRef.current);
      intervalRef.current = null;
    }
    if (reconnectionIntervalRef.current) {
      clearInterval(reconnectionIntervalRef.current);
      reconnectionIntervalRef.current = null;
    }
    if (durationIntervalRef.current) {
      clearInterval(durationIntervalRef.current);
      durationIntervalRef.current = null;
    }
    if (silenceTimeoutRef.current) {
      clearTimeout(silenceTimeoutRef.current);
      silenceTimeoutRef.current = null;
    }
    onCancel?.();
  };

  // Handle automatic reconnection for long recordings
  const handleReconnection = useCallback(async () => {
    if (!onEngineReconnect || !isListening || isReconnecting) return;

    console.log('Initiating reconnection to prevent timeout...');
    setIsReconnecting(true);

    try {
      // Stop current session
      if (engine) {
        await new Promise<void>((resolve) => {
          engine.stopTranscribingAsync(
            () => {
              console.log('Stopped for reconnection');
              resolve();
            },
            (error) => {
              console.error('Error stopping for reconnection:', error);
              resolve();
            }
          );
        });
      }

      // Wait a moment
      await new Promise((resolve) => setTimeout(resolve, 500));

      // Reconnect
      await onEngineReconnect();

      // Wait for engine to be ready
      await new Promise((resolve) => setTimeout(resolve, 500));

      // Restart transcription
      if (engine && isListening) {
        await new Promise<void>((resolve, reject) => {
          engine.startTranscribingAsync(
            () => {
              console.log('Reconnection successful, transcription resumed');
              resolve();
            },
            (error) => {
              console.error('Error restarting after reconnection:', error);
              reject(error);
            }
          );
        });
      }
    } catch (error) {
      console.error('Reconnection failed:', error);
    } finally {
      setIsReconnecting(false);
    }
  }, [engine, isListening, isReconnecting, onEngineReconnect]);

  useEffect(() => {
    if (engine) {
      engine.transcribing = handleTranscribing;
    }

    return () => {
      setTranscript('');
      setTranscriptHistory(['']);
      setTotalRecordingTime(0);
      setLastResumeTime(null);
      if (engine) {
        engine.stopTranscribingAsync();
      }
      // Clear all intervals and timeouts when component unmounts
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
      }
      if (reconnectionIntervalRef.current) {
        clearInterval(reconnectionIntervalRef.current);
      }
      if (durationIntervalRef.current) {
        clearInterval(durationIntervalRef.current);
      }
      if (silenceTimeoutRef.current) {
        clearTimeout(silenceTimeoutRef.current);
      }
    };
  }, [engine, handleTranscribing]);

  // Track duration while recording
  useEffect(() => {
    if (isListening && lastResumeTime) {
      durationIntervalRef.current = setInterval(() => {
        const currentDuration = (Date.now() - lastResumeTime) / 1000;
        // This will trigger re-render and keep duration updated
      }, 1000);
    } else {
      if (durationIntervalRef.current) {
        clearInterval(durationIntervalRef.current);
        durationIntervalRef.current = null;
      }
    }

    return () => {
      if (durationIntervalRef.current) {
        clearInterval(durationIntervalRef.current);
      }
    };
  }, [isListening, lastResumeTime]);

  useEffect(() => {
    if (tEvent) {
      const { Type, Offset, Text } = tEvent;
      if (Type === 'ConversationTranscription') {
        setTranscript(() => {
          if (Offset !== currentOffset) {
            const newHist = [...transcriptHistory, transcript];
            setTranscriptHistory(newHist);
            setCurrentOffset(Offset);
          }
          return Text;
        });
      }
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [tEvent]);

  const toggleRecord = () => {
    if (!engine) {
      console.error('Cannot toggle recording: Speech engine not initialized');
      return;
    }

    if (isListening) {
      // Pausing - accumulate the duration
      if (lastResumeTime) {
        const sessionDuration = (Date.now() - lastResumeTime) / 1000;
        setTotalRecordingTime((prev) => prev + sessionDuration);
        setLastResumeTime(null);
      }

      setIsListening(false);
      engine.stopTranscribingAsync(
        () => {
          setRecordingState(PAUSED);
          console.log('Recording paused successfully');
        },
        (error) => {
          console.error('Error stopping transcription:', error);
          setRecordingState(PAUSED);
        }
      );
    } else {
      // Resuming/Starting
      setIsListening(true);
      setLastResumeTime(Date.now());

      engine.startTranscribingAsync(
        () => {
          setRecordingState(RECORDING);
          console.log('Recording started successfully');
        },
        (error) => {
          console.error('Error starting transcription:', error);
          setIsListening(false);
          setRecordingState(IDLE);
          setLastResumeTime(null);
        }
      );
    }
  };

  useEffect(() => {
    if (wavesEl.current != null && typeof window !== 'undefined') {
      const singleGreenWave = [
        {
          timeModifier: 1.2,
          lineWidth: 1.5,
          amplitude: isSpeaking ? 35 : 0,
          wavelength: 70,
          strokeStyle: '#1FC6A6',
        },
      ];

      new SineWaves({
        el: wavesEl.current,
        speed: wavesConfig.speed,
        ease: 'SineInOut',
        wavesWidth: wavesConfig.wavesWidth,
        waves: currentMode !== LANGUAGE_SELECTION ? singleGreenWave : waves,
        resizeEvent: function () {
          if (currentMode !== LANGUAGE_SELECTION) {
            var gradient = this.ctx.createLinearGradient(0, 0, this.width, 0);
            gradient.addColorStop(0, '#1FC6A6');
            gradient.addColorStop(1, '#1FC6A6');
            var index = -1;
            var length = this.waves.length;
            while (++index < length) {
              this.waves[index].strokeStyle = gradient;
            }
          } else {
            var gradient = this.ctx.createLinearGradient(0, 0, this.width, 0);
            gradient.addColorStop(0, 'rgba(25, 255, 255, 0)');
            gradient.addColorStop(0.5, 'rgba(100, 100, 255, 1)');
            gradient.addColorStop(1, 'rgba(255, 255, 255, 1)');
            var index = -1;
            var length = this.waves.length;
            while (++index < length) {
              this.waves[index].strokeStyle = gradient;
            }
          }
        },
      });
    }
  }, [wavesConfig.speed, wavesConfig.wavesWidth, currentMode, isSpeaking]);

  const handleStartRecord = () => {
    if (!engine) {
      console.error('Speech engine not initialized');
      return;
    }

    setCurrentMode(RECORD_CONSULTATION);
    setRecordingState(RECORDING);
    toggleRecord();
    setWavesConfig({
      speed: 8,
      wavesWidth: '75%',
    });
    // Start periodic action every 5 minutes
    intervalRef.current = setInterval(
      handlePeriodicAction,
      PERIODIC_ACTION_INTERVAL
    );

    // Start reconnection interval to prevent timeout (every 15 minutes)
    if (onEngineReconnect) {
      reconnectionIntervalRef.current = setInterval(
        handleReconnection,
        RECONNECTION_INTERVAL
      );
    }
  };

  const handlePauseRecord = () => {
    setRecordingState(PAUSED);
    toggleRecord();
    setWavesConfig({
      speed: 0,
      wavesWidth: '0%',
    });
    // Clear intervals when recording is paused
    if (intervalRef.current) {
      clearInterval(intervalRef.current);
      intervalRef.current = null;
    }
    if (reconnectionIntervalRef.current) {
      clearInterval(reconnectionIntervalRef.current);
      reconnectionIntervalRef.current = null;
    }
  };

  const handleResumeRecord = () => {
    setRecordingState(RECORDING);
    toggleRecord();
    setWavesConfig({
      speed: 8,
      wavesWidth: '75%',
    });
    // Restart periodic action and reconnection
    intervalRef.current = setInterval(
      handlePeriodicAction,
      PERIODIC_ACTION_INTERVAL
    );
    if (onEngineReconnect) {
      reconnectionIntervalRef.current = setInterval(
        handleReconnection,
        RECONNECTION_INTERVAL
      );
    }
  };

  // Calculate current total duration
  const getCurrentDuration = () => {
    let duration = totalRecordingTime;
    if (isListening && lastResumeTime) {
      duration += (Date.now() - lastResumeTime) / 1000;
    }
    return duration;
  };

  return (
    <div className="w-full min-h-full flex flex-col justify-between flex-1">
      <div>
        {recordingState === IDLE && currentMode === LANGUAGE_SELECTION && (
          <div className="h-full">
            <div className="w-full max-w-xl py-2 px-3 flex flex-col gap-2">
              <span className="mb-2 font-normal text-[12px]">
                To get started, please select your preferred language from the
                options below. This will help us provide you with the best
                possible experience.
              </span>
              <RadioGroup
                value={selectedLanguage}
                onValueChange={handleChooseLanguage}
                className="grid grid-cols-3 gap-4"
              >
                {languageOptions.map((language) => (
                  <label
                    key={language.value}
                    className="flex items-center gap-2 cursor-pointer text-[16px] font-medium"
                  >
                    <RadioGroupItem value={language.value} />
                    {language.label}
                  </label>
                ))}
              </RadioGroup>
            </div>
          </div>
        )}
        {currentMode !== LANGUAGE_SELECTION && (
          <div className="flex flex-col items-center justify-center bg-[#E6EBF0] py-2 px-2 rounded-md mb-4">
            <>
              <span className="text-xs mb-2 text-center text-[#222] font-medium">
                {isReconnecting
                  ? 'Reconnecting...'
                  : `Ambient Recording Active... ${Math.floor(getCurrentDuration() / 60)}:${String(Math.floor(getCurrentDuration() % 60)).padStart(2, '0')}`}
              </span>
              <canvas ref={wavesEl} className="w-full h-20" />
            </>
          </div>
        )}
        {currentMode === RECORD_CONSULTATION && (
          <div
            className="h-[40vh] flex flex-col gap-2 overflow-y-auto px-3"
            ref={transcriptEl}
          >
            <span className="text-[18px] font-medium font-['Inter'] pb-2 block">
              Live Transcript:
            </span>
            <div className="gap-3">
              {[...transcriptHistory, transcript].map(
                (x, i) => x && <p key={i}>{x}</p>
              )}
            </div>
          </div>
        )}
      </div>
      <div
        className={
          isLanguageSelection
            ? 'border-t border-[#DAE1E7] py-4 px-5 flex items-center justify-center'
            : 'border-t border-[#DAE1E7] py-3 px-5 flex items-center justify-between'
        }
      >
        <div
          className={isLanguageSelection ? 'hidden' : 'flex items-center gap-5'}
        >
          {(recordingState === RECORDING || recordingState === PAUSED) && (
            <>
              <PrimaryButton
                variant="contained"
                className="bg-[#012436] text-[16px] font-normal w-[180px] h-9 rounded-lg"
                onClick={() => {
                  // Calculate final duration
                  const finalDuration = getCurrentDuration();
                  const finalTranscript = [
                    ...transcriptHistory,
                    transcript,
                  ].join(' ');
                  onRecordEnd(finalTranscript, finalDuration);
                }}
              >
                End recording
                <BiX className="text-xl" />
              </PrimaryButton>
            </>
          )}
        </div>
        {recordingState !== RECORDING && isLanguageSelection && (
          <PrimaryButton
            variant="contained"
            className={`text-[16px] font-normal px-7 h-9 rounded-lg ${
              !engine ? 'bg-gray-400 cursor-not-allowed' : 'bg-[#012436]'
            }`}
            onClick={handleStartRecord}
            disabled={!engine}
          >
            {!engine ? 'Initializing...' : 'Start recording'}
          </PrimaryButton>
        )}
        {recordingState === RECORDING && !isLanguageSelection && (
          <PrimaryButton
            variant="outlined"
            className="text-[16px] font-normal w-[180px] border-[#012436] text-[#012436] h-9 rounded-lg"
            onClick={handlePauseRecord}
          >
            Pause recording
            <TbPlayerPause className="text-lg" />
          </PrimaryButton>
        )}
        {recordingState === PAUSED && !isLanguageSelection && (
          <PrimaryButton
            variant="contained"
            className="bg-[#012436] text-[16px] font-normal h-9 rounded-lg"
            onClick={handleResumeRecord}
          >
            Resume recording
            <TbPlayerPlay className="text-lg" />
          </PrimaryButton>
        )}
      </div>
    </div>
  );
};

export default RecordConsultation;

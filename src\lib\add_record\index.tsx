'use client';

import { useEffect, useRef, useState } from 'react';

import { useForm } from 'react-hook-form';

import { maxBy } from 'lodash';
import * as speechsdk from 'microsoft-cognitiveservices-speech-sdk';
import { BiSave, BiX } from 'react-icons/bi';
import { toast } from 'sonner';

import { useCurrentPatientStore } from '@/store/currentPatientStore';
import {
  EmrTypes,
  useCustomiseEmrStore,
} from '@/store/emr/doctor-profile/customise-emr';
import { useDoctorStore } from '@/store/emr/doctor-profile/personal-info';
import { useAmbientStore } from '@/store/emr/lifestyle/ambient-listening/ambient-store';

import {
  getSpeechToken,
  SummarizeConversationRes,
  summarizeConversation,
  summarizeAmbientListening,
} from '@/query/speech';

import { CurrentModal, currentModal } from '@/constants/ambient-listening';

import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';

import SummaryForm from '@/emr/components/consultation/summary-form';
import Transcript from '@/emr/components/transcript';

import './styles.scss';

import AppModal from '@/core/components/app-modal';
import PrimaryButton from '@/core/components/primary-button';

import Loading from '../common/loading';
import RecordConsultation from '../modals/RecordConsultation';

import { useUserStore } from '@/store/userStore';

const RecordingLanguage = {
  English: 'en-IN',
  Malayalam: 'ml-IN',
  Tamil: 'ta-IN',
  Kannada: 'kn-IN',
  Telugu: 'te-IN',
  Bengali: 'bn-IN',
  Hindi: 'hi-IN',
} as const;

type LanguageType = (typeof RecordingLanguage)[keyof typeof RecordingLanguage];

const {
  INITIAL,
  RECORD_CONSULTATION,
  LOADING,
  SHOW_SUMMARY,
  LANGUAGE_SELECTION,
} = currentModal;

export interface AddRecordProps {
  onSave?: (data: SummarizeConversationRes['summary']) => void;
  disabled?: boolean;
  isLoading?: boolean;
  summaryFormTitle?: string;
  summaryFormSubtitle?: string;
  modalTitle?: string;
  customSummaryForm?: React.ReactNode;
  isAmbientRecord?: boolean;
  source?: string;
}

const AddRecord = ({
  onSave = () => {},
  disabled = false,
  isLoading = false,
  summaryFormTitle = 'Consultation',
  modalTitle = '',
  customSummaryForm,
  isAmbientRecord = false,
  source = '',
}: AddRecordProps) => {
  const { doctorProfile } = useDoctorStore();

  const { customiseEmrData, fetchCustomiseEmr } = useCustomiseEmrStore();
  const { patient } = useCurrentPatientStore();
  const { data: userData } = useUserStore();

  const customEmrData = maxBy<EmrTypes>(
    customiseEmrData,
    (item) => new Date(item.created_on as string)
  );

  const emrLanguage = customEmrData?.preferred_language_for_ambient_listening;

  const doctorLanguage = Object.entries(RecordingLanguage).find(
    ([key]) => key === emrLanguage
  )?.[1];

  const [engine, setEngine] =
    useState<speechsdk.ConversationTranscriber | null>(null);
  const [response, setResponse] = useState<SummarizeConversationRes | null>(
    null
  );
  const [currentMode, setCurrentMode] = useState<CurrentModal>(INITIAL);
  const [language, setLanguage] = useState<LanguageType>(
    RecordingLanguage.English
  );
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [isEngineInitializing, setIsEngineInitializing] = useState(false);
  const [shouldAutoRestart, setShouldAutoRestart] = useState(false);
  const engineRef = useRef<speechsdk.ConversationTranscriber | null>(null);
  const tokenRefreshIntervalRef = useRef<NodeJS.Timeout | null>(null);

  const form = useForm<SummarizeConversationRes['summary']>();

  const startFlow = () => {
    setCurrentMode(LANGUAGE_SELECTION);
    setIsModalOpen(true);
  };

  const handleClose = () => {
    if (isLoading) return; // Prevent closing while saving

    // Clean up engine
    if (engineRef.current) {
      try {
        engineRef.current.stopTranscribingAsync(() => {
          engineRef.current?.close();
        });
      } catch (error) {
        console.error('Error closing engine:', error);
      }
      engineRef.current = null;
    }

    setCurrentMode(INITIAL);
    setLanguage(doctorLanguage || RecordingLanguage.English);
    setEngine(null);
    setResponse(null);
    setShouldAutoRestart(false);
    setIsModalOpen(false);

    // Clean up token refresh interval
    if (tokenRefreshIntervalRef.current) {
      clearInterval(tokenRefreshIntervalRef.current);
      tokenRefreshIntervalRef.current = null;
    }
  };

  const initializeSpeechEngine = async (language: string) => {
    try {
      setIsEngineInitializing(true);

      // Clean up existing engine if any
      if (engineRef.current) {
        try {
          engineRef.current.stopTranscribingAsync(() => {
            engineRef.current?.close();
          });
        } catch (error) {
          console.error('Error closing previous engine:', error);
        }
        engineRef.current = null;
      }

      const speechToken = await getSpeechToken();
      if (!speechToken) {
        throw new Error('Failed to get speech token');
      }

      const speechConfig = speechsdk.SpeechConfig.fromAuthorizationToken(
        speechToken,
        'eastus'
      );

      speechConfig.speechRecognitionLanguage = language;

      // CRITICAL: Set properties to handle long recordings
      // Use very long timeouts to support 1+ hour recordings
      speechConfig.setProperty(
        speechsdk.PropertyId.SpeechServiceConnection_InitialSilenceTimeoutMs,
        '7200000' // 2 hours (120 minutes)
      );

      speechConfig.setProperty(
        speechsdk.PropertyId.SpeechServiceConnection_EndSilenceTimeoutMs,
        '7200000' // 2 hours (120 minutes)
      );

      // Set segmentation silence timeout to handle pauses
      speechConfig.setProperty(
        speechsdk.PropertyId.Speech_SegmentationSilenceTimeoutMs,
        '2000' // 2 seconds for sentence segmentation
      );

      // Set overall recognition timeout for long sessions
      speechConfig.setProperty(
        'SpeechServiceConnection_RecoTimeoutMs',
        '7200000' // 2 hours (120 minutes)
      );

      // Additional connection stability settings
      speechConfig.setProperty(
        'SpeechServiceConnection_AutoReconnectTimeoutMs',
        '30000' // 30 seconds for auto-reconnect
      );

      speechConfig.setProperty(
        'SpeechServiceConnection_EnableAudioLogging',
        'false' // Disable audio logging for better performance
      );

      // Enable continuous recognition mode
      speechConfig.setProperty(
        speechsdk.PropertyId.SpeechServiceConnection_RecoMode,
        'CONVERSATION'
      );

      // Enable detailed results
      speechConfig.requestWordLevelTimestamps();
      speechConfig.outputFormat = speechsdk.OutputFormat.Detailed;

      const audioConfig = speechsdk.AudioConfig.fromDefaultMicrophoneInput();
      const recognizer = new speechsdk.ConversationTranscriber(
        speechConfig,
        audioConfig
      );

      // Add session started handler
      recognizer.sessionStarted = (s, e) => {
        console.log('Session started:', e.sessionId);
      };

      // Add session stopped handler
      recognizer.sessionStopped = (s, e) => {
        console.log('Session stopped:', e.sessionId);
      };

      // Add canceled handler to catch errors
      recognizer.canceled = (s, e) => {
        console.error('Recognition canceled:', e.reason, e.errorDetails);
        if (e.reason === speechsdk.CancellationReason.Error) {
          console.error(`Recognition error details: ${e.errorDetails}`);

          // Handle connection errors by triggering automatic reconnection
          if (
            e.errorDetails &&
            (e.errorDetails.includes('Unable to contact server') ||
              e.errorDetails.includes('WebSocket') ||
              e.errorDetails.includes('StatusCode: 0'))
          ) {
            console.log(
              'Connection error detected, attempting automatic reconnection...'
            );
            // Trigger reconnection after a short delay
            setTimeout(async () => {
              try {
                setShouldAutoRestart(true);
                await initializeSpeechEngine(language);
              } catch (error) {
                console.error('Auto-reconnection failed:', error);
                toast.error(
                  'Connection lost. Please try restarting the recording.'
                );
              }
            }, 2000);
          } else if (!shouldAutoRestart) {
            // Only show user-facing errors for non-connection issues
            toast.error(`Recognition error: ${e.errorDetails}`);
          }
        }
      };

      engineRef.current = recognizer;
      setEngine(recognizer);
      console.log('Speech engine initialized successfully');

      // If we should auto-restart (after reconnection), start transcribing
      if (shouldAutoRestart) {
        console.log('Auto-restarting transcription after reconnection');
        setTimeout(() => {
          recognizer.startTranscribingAsync(
            () => {
              console.log('Transcription auto-resumed successfully');
              setShouldAutoRestart(false);
            },
            (error) => {
              console.error('Error auto-resuming transcription:', error);
              setShouldAutoRestart(false);
            }
          );
        }, 1000);
      }

      // Set up proactive token refresh every 8 minutes (before 10-minute expiration)
      if (tokenRefreshIntervalRef.current) {
        clearInterval(tokenRefreshIntervalRef.current);
      }
      tokenRefreshIntervalRef.current = setInterval(
        async () => {
          console.log('Proactively refreshing speech token...');
          try {
            setShouldAutoRestart(true);
            await initializeSpeechEngine(language);
          } catch (error) {
            console.error('Proactive token refresh failed:', error);
          }
        },
        8 * 60 * 1000
      ); // 8 minutes

      return recognizer;
    } catch (error) {
      console.error('Error initializing speech engine:', error);
      if (!shouldAutoRestart) {
        toast.error(
          'Failed to initialize speech recognition. Please try again.'
        );
      }
      throw error;
    } finally {
      setIsEngineInitializing(false);
    }
  };

  const handleChooseLanguage = async (newLanguage: string) => {
    setLanguage(newLanguage as any);
    setCurrentMode(LOADING);
    try {
      await initializeSpeechEngine(newLanguage);
      setCurrentMode(LANGUAGE_SELECTION);
    } catch (error) {
      console.error('Error initializing speech engine:', error);
      toast.error('Failed to initialize speech engine. Please try again.');
      setCurrentMode(LANGUAGE_SELECTION);
    }
  };

  const handleEngineReconnect = async () => {
    console.log(
      'Handling engine reconnection - getting fresh token and engine...'
    );
    try {
      // Mark that we should auto-restart after reconnection
      setShouldAutoRestart(true);
      await initializeSpeechEngine(language);
      console.log('Engine reconnected successfully');
    } catch (error) {
      console.error('Failed to reconnect engine:', error);
      setShouldAutoRestart(false);
      throw error;
    }
  };

  const { setConversation, setSummary, setLoading } = useAmbientStore();

  const handleTranscript = async (transcript: string, duration: number) => {
    try {
      setCurrentMode(LOADING);
      setLoading(true);

      if (isAmbientRecord && source) {
        // Validate that source is not empty or null
        if (!source || source.trim() === '') {
          console.error('Invalid source for ambient listening:', source);
          toast.error(
            'Please navigate to a lifestyle category first before using ambient listening'
          );
          throw new Error('Invalid source for ambient listening');
        }

        // Validate that transcript is not empty
        if (!transcript || transcript.trim() === '') {
          console.error(
            'Empty transcript for ambient listening. Engine ready:',
            !!engine
          );
          toast.error(
            'No audio transcript available. Please try recording again.'
          );
          throw new Error('Empty transcript for ambient listening');
        }

        try {
          const ambientResponse = await summarizeAmbientListening(
            source,
            transcript
          );

          // Update the ambient store with the response
          if (ambientResponse.conversation) {
            setConversation(ambientResponse.conversation);
          }
          if (ambientResponse.summary) {
            setSummary({
              ...ambientResponse.summary,
              recordingDuration: duration,
            });
          }

          // Also update local state if needed
          setResponse({
            conversation: ambientResponse.conversation || [],
            summary: {
              ...ambientResponse.summary,
              recordingDuration: duration,
            },
          });
        } catch (error) {
          console.error('Error in ambient listening:', error);
          toast.error('Error processing ambient data');
          throw error;
        } finally {
          setLoading(false);
        }
      } else {
        try {
          const consultationResponse = await summarizeConversation(transcript);
          setResponse({
            ...consultationResponse,
            summary: {
              ...consultationResponse.summary,
              recordingDuration: duration,
            },
          });
        } catch (error) {
          console.error('Error in conversation summary:', error);
          throw error;
        }
      }

      setCurrentMode(SHOW_SUMMARY);
    } catch (error) {
      if (!isAmbientRecord) {
        toast.error('Error in summarizing conversation');
      }
      setCurrentMode(LANGUAGE_SELECTION);
      setResponse(null);
      console.error('Error in handleTranscript:', error);
      setLoading(false);
    }
  };

  // Create a function to handle the save operation
  const handleSaveOperation = async () => {
    if (isAmbientRecord && source) {
      // For ambient records, we already have the data in response
      if (response?.summary) {
        await onSave(response.summary);
      }
    } else {
      // For non-ambient records, we need to submit the form
      await form.handleSubmit(async (submitted) => {
        // Always include all data from response regardless of which tab is active
        const completeData = {
          ...submitted,
          ...(response?.conversation && {
            conversation: response.conversation,
          }),
          ...(response?.summary?.recordingDuration && {
            recordingDuration: response.summary.recordingDuration,
          }),
          // Include any other fields from the response summary
          ...response?.summary,
        };
        await onSave(completeData);
      })();
    }
  };

  // The save handler that will be called when the save button is clicked
  const handleSave = async () => {
    try {
      await handleSaveOperation();
      // Only close the modal if save was successful
      handleClose();
    } catch (error) {
      console.error('Error saving record:', error);
      const errorMessage =
        isAmbientRecord && source
          ? 'Error saving ambient record'
          : 'Error saving record';
      toast.error(errorMessage);
      // Don't close the modal on error
    }
  };

  const getModalTitle = () => {
    switch (currentMode) {
      case LANGUAGE_SELECTION:
        return 'Choose Language';
      case RECORD_CONSULTATION:
        return 'Recording and live transcription';
      case LOADING:
        return 'Processing...';
      case SHOW_SUMMARY:
        return 'Consultation Summary';
      default:
        return 'Ambient Listening';
    }
  };

  useEffect(() => {
    if (doctorProfile?.id) {
      fetchCustomiseEmr(doctorProfile?.id as string);
    }
  }, [doctorProfile?.id, fetchCustomiseEmr]);

  useEffect(() => {
    if (doctorLanguage) {
      setLanguage(doctorLanguage);
    } else {
      setLanguage(RecordingLanguage.English);
    }
  }, [doctorLanguage]);

  useEffect(() => {
    if (
      currentMode === LANGUAGE_SELECTION &&
      !engine &&
      !isEngineInitializing
    ) {
      initializeSpeechEngine(language).catch((error) => {
        console.error('Error initializing speech engine on modal open:', error);
      });
    }
  }, [currentMode, language, engine, isEngineInitializing]);

  // Sync engineRef with engine state
  useEffect(() => {
    engineRef.current = engine;
  }, [engine]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (tokenRefreshIntervalRef.current) {
        clearInterval(tokenRefreshIntervalRef.current);
        tokenRefreshIntervalRef.current = null;
      }
    };
  }, []);

  return (
    <>
      <button
        className={`text-sm flex items-center justify-center gap-1.5 py-1.5 w-full rounded-md ${
          disabled
            ? 'bg-gray-300 text-gray-500 cursor-not-allowed'
            : 'bg-[#4B6BFB] text-white hover:bg-[#3a56d4]'
        }`}
        onClick={!disabled ? startFlow : undefined}
        disabled={disabled}
      >
        Ambient Listening
      </button>

      <AppModal
        open={isModalOpen}
        onClose={handleClose}
        title={
          currentMode === LANGUAGE_SELECTION ||
          currentMode === RECORD_CONSULTATION
            ? modalTitle || getModalTitle()
            : ''
        }
        classes={{
          root:
            currentMode === LANGUAGE_SELECTION
              ? ' max-h-[95vh]  overflow-auto'
              : 'max-h-[95vh] min-h-[65vh] overflow-auto min-w-[60vw]',
          body: '!p-0 flex flex-col flex-1 min-h-0',
        }}
      >
        <section className="flex flex-col flex-1 min-h-0 w-full">
          {(currentMode === LANGUAGE_SELECTION ||
            currentMode === RECORD_CONSULTATION) && (
            <RecordConsultation
              engine={engine}
              onRecordEnd={handleTranscript}
              handleChooseLanguage={handleChooseLanguage}
              selectedLanguage={language}
              currentMode={currentMode}
              setCurrentMode={setCurrentMode}
              isLanguageSelection={currentMode === LANGUAGE_SELECTION}
              onEngineReconnect={handleEngineReconnect}
            />
          )}

          {currentMode === LOADING && (
            <div className="flex flex-1 min-h-[65vh] items-center justify-center w-full">
              <Loading />
            </div>
          )}

          {currentMode === SHOW_SUMMARY && (
            <>
              <div className="flex flex-col h-full max-h-[80vh] relative pb-16">
                <Tabs
                  defaultValue="conversation"
                  className="w-full flex flex-col flex-1 min-h-0"
                >
                  <div className="bg-white sticky top-0 z-10 py-5 pl-6.5 pr-7.5 text-xl font-semibold">
                    <TabsList className="flex w-full justify-between">
                      <TabsTrigger
                        className="flex-1 text-center"
                        value="summary"
                      >
                        {`${summaryFormTitle} Summary (Generated)`}
                      </TabsTrigger>
                      <TabsTrigger
                        className="flex-1 text-center"
                        value="conversation"
                      >
                        Transcription
                      </TabsTrigger>
                    </TabsList>
                  </div>

                  <div className="overflow-y-auto flex-1">
                    <TabsContent
                      className="pb-5 pl-6 pr-7.5 min-h-0"
                      value="summary"
                    >
                      {customSummaryForm ? (
                        <>{customSummaryForm}</>
                      ) : (
                        <SummaryForm data={response} form={form} editable />
                      )}
                    </TabsContent>

                    <TabsContent className="min-h-0" value="conversation">
                      <div className="pb-5 pl-6 pr-7.5">
                        {response ? (
                          <Transcript
                            conversation={response?.conversation}
                            duration={response?.summary?.recordingDuration}
                            patientName={patient?.name || 'Patient'}
                            doctorName={
                              doctorProfile?.general?.fullName ||
                              userData.name ||
                              'Doctor'
                            }
                          />
                        ) : (
                          <div className="flex items-center justify-center text-gray-500 h-40">
                            Transcription not generated
                          </div>
                        )}
                      </div>
                    </TabsContent>
                  </div>
                </Tabs>

                <div className="fixed bottom-0 left-0 right-0 bg-white border-t border-t-[#C2CDD6] z-20 shadow-md">
                  <div className="flex w-full px-6.5 py-3 justify-end gap-4">
                    <PrimaryButton
                      variant="outlined"
                      className="text-[16px] font-normal px-4 h-9 rounded-xl border-black text-black"
                      onClick={handleClose}
                    >
                      Close
                      <BiX />
                    </PrimaryButton>

                    <PrimaryButton
                      variant="contained"
                      className="bg-[#012436] text-white text-[16px] font-normal px-4 h-9 rounded-xl ml-auto"
                      onClick={handleSave}
                      disabled={isLoading}
                      isLoading={isLoading}
                    >
                      Save Record
                      <BiSave />
                    </PrimaryButton>
                  </div>
                </div>
              </div>
            </>
          )}
        </section>
      </AppModal>
    </>
  );
};

export default AddRecord;
